<?php
// Get the instructor's college code
$user = Auth::user();
$collegeCode = $user->college_code;

// If college_code is not set in user table, try to get it from instructor_infos
if (empty($collegeCode)) {
    $instructorInfo = \App\instructors_infos::where('instructor_id', $user->id)->first();
    $collegeCode = $instructorInfo ? $instructorInfo->college : null;
}

// Get schedules for this instructor
$query = \App\room_schedules::distinct()
    ->where('is_active', 1)
    ->where('instructor', $user->id);

// If college code is available, filter by college
if (!empty($collegeCode)) {
    $query = $query->whereExists(function ($subquery) use ($collegeCode) {
        $subquery->select(DB::raw(1))
            ->from('offerings_infos')
            ->join('curricula', 'offerings_infos.curriculum_id', '=', 'curricula.id')
            ->whereRaw('offerings_infos.id = room_schedules.offering_id')
            ->where('curricula.college_code', $collegeCode);
    });
}

$tabular_schedules = $query->get(['offering_id', 'is_loaded']);
?>



<?php $__env->startSection('main-content'); ?>
<link rel="stylesheet" href="<?php echo e(asset ('plugins/toastr/toastr.css')); ?>">
<link rel="stylesheet" href="<?php echo e(asset ('plugins/fullcalendar/fullcalendar.css')); ?>">
<link rel="stylesheet" href="<?php echo e(asset ('plugins/datatables/dataTables.bootstrap.css')); ?>">
<link rel="stylesheet" href="<?php echo e(asset ('plugins/datatables/jquery.dataTables.css')); ?>">
<section class="content-header">
    <h1><i class="fa fa-bullhorn"></i>
        Faculty Loading Assignment
        <small></small>
    </h1>
    <ol class="breadcrumb">
        <li><a href="<?php echo e(url('/')); ?>"><i class="fa fa-home"></i> Home</a></li>
        <li class="active">Faculty Loading Assignment</li>
    </ol>
</section>

<div class="container-fluid" style="margin-top:20px;">
    <div class="row">
        <div class="col-sm-3">
            <div class="box box-default">
                <div class="box-body no-padding">
                    <table class="table table-borderless table-condensed">
                        <tr>
                            <td align="center"><label class="label label-danger">Legend</label></td>
                        </tr>
                        <tr style="background:lightsalmon; margin: 0; padding:0px">
                            <td align="center"><label style="margin:0;" class="callout callout-danger">Schedule w/ red color indicates that the admin suggests you this load.</label></td>
                        </tr>
                        <tr style="background:lightblue;">
                            <td align="center"><label style="margin:0;" class="callout callout-info">Schedule w/ blue color indicates that this event is your current load.</label></td>
                        </tr>
                    </table>
                </div>
            </div>
            <div class="box box-danger box-solid">
                <div class="box box-body">
                    <div class="table-responsive" id="reloadtabular">
                        <?php if(!$tabular_schedules->isEmpty()): ?>
                        <div align="center" style="margin-bottom:10px;">
                            <label class="label label-danger">Schedules Suggested</label>
                        </div>
                        <table class="table table-bordered table-striped">
                            <thead>
                                <tr>
                                    <th>Code</th>
                                    <th>Section</th>
                                    <th>Schedule</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php $__currentLoopData = $tabular_schedules; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $schedule): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <?php
                                $offering = \App\offerings_infos_table::with('curriculum.subject')->find($schedule->offering_id);
                                $curriculum = $offering ? $offering->curriculum : null;
                                $course_detail = new \stdClass();
                                $course_detail->course_code = $curriculum ? $curriculum->control_code : 'N/A';
                                $course_detail->course_name = $curriculum && $curriculum->subject ? $curriculum->subject->subject_name : ($curriculum ? $curriculum->control_code : 'N/A');
                                $course_detail->section_name = $offering ? $offering->section_name : 'N/A';
                                ?>
                                <?php if($schedule->is_loaded == 0): ?>
                                <tr style="background:lightsalmon;" onclick="show_offer('<?php echo e($schedule->offering_id); ?>')">
                                    <?php else: ?>
                                <tr style="background:lightblue;">
                                    <?php endif; ?>
                                    <td><?php echo e($course_detail->course_code); ?></td>
                                    <td><?php echo e($course_detail->section_name); ?></td>
                                    <td>
                                        <div align="center">
                                            <?php
                                            $schedule3s = \App\room_schedules::distinct()->where('offering_id', $schedule->offering_id)->get(['room']);
                                            ?>
                                            <?php $__currentLoopData = $schedule3s; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $schedule3): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                            <?php echo e($schedule3->room); ?>

                                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                            <br>
                                            <?php
                                            $schedule2s = \App\room_schedules::distinct()->where('offering_id', $schedule->offering_id)->get(['time_starts', 'time_end', 'room']);
                                            ?>
                                            <?php $__currentLoopData = $schedule2s; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $schedule2): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                            <?php
                                            $days = \App\room_schedules::where('offering_id', $schedule->offering_id)->where('time_starts', $schedule2->time_starts)->where('time_end', $schedule2->time_end)->where('room', $schedule2->room)->get(['day']);
                                            ?>
                                            <!--                <?php $__currentLoopData = $days; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $day): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?><?php echo e($day->day); ?><?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?> <?php echo e($schedule2->time); ?> <br>-->
                                            [<?php $__currentLoopData = $days; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $day): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?><?php echo e($day->day); ?><?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?> <?php echo e(date('g:iA', strtotime($schedule2->time_starts))); ?>-<?php echo e(date('g:iA', strtotime($schedule2->time_end))); ?>]<br>
                                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                        </div>
                                    </td>
                                </tr>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                <small class="text-muted" >Click the Schedule below to confirm and accept the load..</small>
                            </tbody>
                        </table>
                        <?php else: ?>
                        <div class="callout callout-warning">
                            <div align="center"><h5>No Faculty Loading Found!</h5></div>
                        </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-sm-9">
            <div class="nav-tabs-custom">
                <ul class="nav nav-tabs">
                    <li class="active"><a href="#tab_1" data-toggle="tab">Calendar View</a></li>
                    <li><a href="#tab_2" data-toggle="tab">Tabular View</a></li>
                    <li class="pull-right"><a href="#" class="text-muted"><i class="fa fa-gear"></i></a></li>
                </ul>
                <div class="tab-content">
                    <div class="tab-pane active" id="tab_1">
                        <div class="box-body no-padding">
                            <div id="calendar"></div>
                        </div>
                    </div>
                    <div class="tab-pane" id="tab_2">
                        <div class="table-responsive">
                            <?php if(!$tabular_schedules->isEmpty()): ?>
                            <table class="table table-bordered table-striped">
                                <thead>
                                    <tr>
                                        <th>Code</th>
                                        <th>Description</th>
                                        <th>Section</th>
                                        <th>Schedule</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php $__currentLoopData = $tabular_schedules; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $schedule): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <?php
                                    $offering = \App\offerings_infos_table::with('curriculum.subject')->find($schedule->offering_id);
                                    $curriculum = $offering ? $offering->curriculum : null;
                                    $course_detail = new \stdClass();
                                    $course_detail->course_code = $curriculum ? $curriculum->control_code : 'N/A';
                                    $course_detail->course_name = $curriculum && $curriculum->subject ? $curriculum->subject->subject_name : ($curriculum ? $curriculum->control_code : 'N/A');
                                    $course_detail->section_name = $offering ? $offering->section_name : 'N/A';
                                    ?>
                                    <tr onclick="remove_faculty_load('<?php echo e($schedule->offering_id); ?>')">
                                        <td><?php echo e($course_detail->course_code); ?></td>
                                        <td><?php echo e($course_detail->course_name); ?></td>
                                        <td><?php echo e($course_detail->section_name); ?></td>
                                        <td>
                                            <div align="center">
                                                <?php
                                                $schedule3s = \App\room_schedules::distinct()->where('offering_id', $schedule->offering_id)->get(['room']);
                                                ?>
                                                <?php $__currentLoopData = $schedule3s; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $schedule3): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                <?php echo e($schedule3->room); ?>

                                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                                <br>
                                                <?php
                                                $schedule2s = \App\room_schedules::distinct()->where('offering_id', $schedule->offering_id)->get(['time_starts', 'time_end', 'room']);
                                                ?>
                                                <?php $__currentLoopData = $schedule2s; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $schedule2): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                <?php
                                                $days = \App\room_schedules::where('offering_id', $schedule->offering_id)->where('time_starts', $schedule2->time_starts)->where('time_end', $schedule2->time_end)->where('room', $schedule2->room)->get(['day']);
                                                ?>
                                                <!--                <?php $__currentLoopData = $days; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $day): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?><?php echo e($day->day); ?><?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?> <?php echo e($schedule2->time); ?> <br>-->
                                                [<?php $__currentLoopData = $days; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $day): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?><?php echo e($day->day); ?><?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?> <?php echo e(date('g:iA', strtotime($schedule2->time_starts))); ?>-<?php echo e(date('g:iA', strtotime($schedule2->time_end))); ?>]<br>
                                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                            </div>
                                        </td>
                                    </tr>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </tbody>
                            </table>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
                <!-- /.tab-content -->
            </div>
        </div>
    </div>
</div>

<div id="displaymodal">

</div>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('footer-script'); ?>
<script type="text/javascript" src="<?php echo e(asset('/plugins/moment/moment.js')); ?>"></script>
<script src="<?php echo e(asset('plugins/fullcalendar/fullcalendar.js')); ?>"></script>
<script src="<?php echo e(asset('plugins/jQueryUI/jquery-ui.js')); ?>"></script>
<script src="<?php echo e(asset('plugins/datatables/jquery.dataTables.js')); ?>"></script>
<script src="<?php echo e(asset('plugins/datatables/dataTables.bootstrap.js')); ?>"></script>
<script>

$('#calendar').fullCalendar({
header: false,
minTime: '07:00:00',
    maxTime: '22:00:00',
        hiddenDays: [0],
        firstDay: 1,
        height: 650,
        allDaySlot: false,
        columnFormat: 'ddd',
        defaultView: 'agendaWeek',
        eventSources: ["<?php echo e(url('/ajax/instructor/reloadcalendar')); ?>"],
        eventRender: function(event, element) {
        element.find('div.fc-title').html(element.find('div.fc-title').text());
        }
})

        function show_offer(offering_id){
        var array = {};
        array['offering_id'] = offering_id;
        $.ajax({
        type: "GET",
                url: "/ajax/instructor/get_offer_load",
                data: array,
                success: function(data){
                $('#displaymodal').html(data).fadeIn();
                $('#modal-primary').modal('show');
                }, error: function(){

        }
        })
        }

function reject_offer(offering_id, reason){
if (reason == ""){
toastr.warning('Please State your Reason!', 'Notification!');
} else{
var array = {};
array['offering_id'] = offering_id;
array['reason'] = reason;
$.ajax({
type: "GET",
        url: "/ajax/instructor/reject_offer",
        data: array,
        success: function(data){
        $('#modal-primary').modal('toggle');
        reloadtabular();
        $('#calendar').fullCalendar('refetchEvents')
                toastr.warning('You have notified the admin why you reject the schedule.', 'Notification!');
        }, error: function(){
toastr.error('Something Went Wrong!', 'Message!');
}
})
}
}

function accept_load(offering_id, reason){
var array = {};
array['offering_id'] = offering_id;
array['reason'] = reason;
$.ajax({
type: "GET",
        url: "/ajax/instructor/accept_load",
        data: array,
        success: function(data){
        $('#modal-primary').modal('toggle');
        reloadtabular();
        $('#calendar').fullCalendar('refetchEvents')
                toastr.info('You have notified the admin that you accepted the schedule', 'Notification!');
        }, error: function(){
toastr.error('Something Went Wrong!', 'Message!');
}
})
}

function reloadtabular(){
$.ajax({
type: "GET",
        url: "/ajax/instructor/reloadtabular",
        success: function(data){
        $('#reloadtabular').html(data).fadeIn();
        }, error: function(){
toastr.error('Something Went Wrong!', 'Message!');
}
})
}
</script>
<?php $__env->stopSection(); ?>
<?php echo $__env->make('layouts.instructor', array_except(get_defined_vars(), array('__data', '__path')))->render(); ?>